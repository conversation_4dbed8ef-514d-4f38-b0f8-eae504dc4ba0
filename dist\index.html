<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Wheat stalks container -->
    <div class="wheat-field">
        <div class="wheat-stalk" data-index="0"></div>
        <div class="wheat-stalk" data-index="1"></div>
        <div class="wheat-stalk" data-index="2"></div>
        <div class="wheat-stalk" data-index="3"></div>
        <div class="wheat-stalk" data-index="4"></div>
        <div class="wheat-stalk" data-index="5"></div>
        <div class="wheat-stalk" data-index="6"></div>
        <div class="wheat-stalk" data-index="7"></div>
        <div class="wheat-stalk" data-index="8"></div>
        <div class="wheat-stalk" data-index="9"></div>
        <div class="wheat-stalk" data-index="10"></div>
        <div class="wheat-stalk" data-index="11"></div>
        <div class="wheat-stalk" data-index="12"></div>
        <div class="wheat-stalk" data-index="13"></div>
        <div class="wheat-stalk" data-index="14"></div>
        <div class="wheat-stalk" data-index="15"></div>
        <div class="wheat-stalk" data-index="16"></div>
        <div class="wheat-stalk" data-index="17"></div>
        <div class="wheat-stalk" data-index="18"></div>
        <div class="wheat-stalk" data-index="19"></div>
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Wheat stalks wind animation
        let mouseX = window.innerWidth / 2; // Start at center
        const wheatStalks = document.querySelectorAll('.wheat-stalk');

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
            updateWheatAnimation();
        });

        function updateWheatAnimation() {
            const windowWidth = window.innerWidth;
            const centerZone = windowWidth * 0.1; // 10% center zone
            const centerX = windowWidth / 2;

            // Calculate distance from center
            const distanceFromCenter = Math.abs(mouseX - centerX);

            // Check if mouse is in center dead zone
            if (distanceFromCenter <= centerZone / 2) {
                // No wind in center zone
                wheatStalks.forEach(stalk => {
                    stalk.style.transform = 'rotate(0deg)';
                });
                return;
            }

            // Calculate wind direction and strength
            const direction = mouseX > centerX ? 1 : -1; // 1 for right, -1 for left
            const maxDistance = (windowWidth / 2) - (centerZone / 2);
            const windStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);

            // Apply wind effect to each stalk
            wheatStalks.forEach((stalk, index) => {
                const stalkVariation = 0.8 + (Math.sin(index * 0.5) * 0.4); // Variation between stalks
                const maxRotation = 25; // Maximum rotation in degrees
                const rotation = direction * windStrength * maxRotation * stalkVariation;

                stalk.style.transform = `rotate(${rotation}deg)`;
                stalk.style.transformOrigin = 'bottom center';
            });
        }

        // Initialize wheat animation
        document.addEventListener('DOMContentLoaded', function() {
            updateWheatAnimation();
        });
    </script>
</body>
</html>
