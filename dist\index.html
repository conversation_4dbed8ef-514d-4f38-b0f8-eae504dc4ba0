<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Wheat stalks container - stalks will be generated dynamically based on screen width -->
    <div class="wheat-field" id="wheatField">
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Dynamic wheat field generation and animation
        let mouseX = window.innerWidth / 2; // Start at center
        let wheatStalks = [];
        let stalkData = []; // Store stalk properties
        let gustSystem = {
            active: false,
            position: 0,
            direction: 1,
            strength: 0,
            width: 15,
            speed: 2,
            nextGustTime: 0
        };

        // Generate wheat stalks based on screen width
        function generateWheatStalks() {
            const wheatField = document.getElementById('wheatField');
            const windowWidth = window.innerWidth;

            // Calculate number of stalks based on screen width (tripled for denser field)
            let stalkCount;
            if (windowWidth >= 1920) {
                stalkCount = 450; // Ultra-wide screens (150 x 3)
            } else if (windowWidth >= 1440) {
                stalkCount = 360; // Large screens (120 x 3)
            } else if (windowWidth >= 1024) {
                stalkCount = 300; // Desktop (100 x 3)
            } else if (windowWidth >= 768) {
                stalkCount = 240; // Tablet (80 x 3)
            } else if (windowWidth >= 480) {
                stalkCount = 180; // Mobile (60 x 3)
            } else {
                stalkCount = 120; // Small mobile (40 x 3)
            }

            // Clear existing stalks
            wheatField.innerHTML = '';
            wheatStalks = [];
            stalkData = [];

            // Generate height variations
            const heights = [68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95];

            // Create stalks
            for (let i = 0; i < stalkCount; i++) {
                const stalk = document.createElement('div');
                stalk.className = 'wheat-stalk';
                stalk.setAttribute('data-index', i);

                // Assign random height
                const randomHeight = heights[Math.floor(Math.random() * heights.length)];
                stalk.style.height = randomHeight + 'px';

                // Store stalk data for wind calculations
                const stalkInfo = {
                    element: stalk,
                    height: randomHeight,
                    flexibility: randomHeight / 95, // Taller stalks are more flexible (0.7 to 1.0)
                    naturalSway: Math.random() * 0.3 + 0.8, // Individual character (0.8 to 1.1)
                    position: i / stalkCount // Position across screen (0 to 1)
                };

                wheatField.appendChild(stalk);
                wheatStalks.push(stalk);
                stalkData.push(stalkInfo);
            }
        }

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
        });

        // Gust system management
        function updateGustSystem() {
            const currentTime = Date.now();

            if (!gustSystem.active && currentTime >= gustSystem.nextGustTime) {
                // Start a new gust
                gustSystem.active = true;
                gustSystem.position = Math.random() < 0.5 ? -gustSystem.width : 100 + gustSystem.width;
                gustSystem.direction = gustSystem.position < 0 ? 1 : -1;
                gustSystem.strength = Math.random() * 0.6 + 0.4; // 0.4 to 1.0
                gustSystem.width = Math.random() * 15 + 10; // 10 to 25 stalks affected
                gustSystem.speed = Math.random() * 1.5 + 1; // 1 to 2.5 speed
            }

            if (gustSystem.active) {
                // Move the gust across the screen
                gustSystem.position += gustSystem.direction * gustSystem.speed;

                // Check if gust has passed through
                if (gustSystem.direction > 0 && gustSystem.position > 100 + gustSystem.width) {
                    gustSystem.active = false;
                    gustSystem.nextGustTime = currentTime + Math.random() * 3000 + 2000; // 2-5 seconds
                } else if (gustSystem.direction < 0 && gustSystem.position < -gustSystem.width) {
                    gustSystem.active = false;
                    gustSystem.nextGustTime = currentTime + Math.random() * 3000 + 2000; // 2-5 seconds
                }
            }
        }

        function updateWheatAnimation() {
            const windowWidth = window.innerWidth;
            const centerZone = windowWidth * 0.1; // 10% center zone
            const centerX = windowWidth / 2;

            // Calculate mouse wind effect
            const distanceFromCenter = Math.abs(mouseX - centerX);
            let mouseWindStrength = 0;
            let mouseDirection = 0;

            if (distanceFromCenter > centerZone / 2) {
                mouseDirection = mouseX > centerX ? 1 : -1;
                const maxDistance = (windowWidth / 2) - (centerZone / 2);
                mouseWindStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);
            }

            // Update gust system
            updateGustSystem();

            // Apply wind effects to each stalk
            stalkData.forEach((stalkInfo, index) => {
                let totalRotation = 0;

                // Mouse wind effect
                if (mouseWindStrength > 0) {
                    const mouseRotation = mouseDirection * mouseWindStrength * 25 * stalkInfo.flexibility * stalkInfo.naturalSway;
                    totalRotation += mouseRotation;
                }

                // Gust wind effect
                if (gustSystem.active) {
                    const stalkPosition = stalkInfo.position * 100; // Convert to percentage
                    const distanceFromGust = Math.abs(stalkPosition - gustSystem.position);

                    if (distanceFromGust <= gustSystem.width) {
                        // Stalk is within gust range
                        const gustInfluence = Math.max(0, 1 - (distanceFromGust / gustSystem.width));
                        const gustRotation = gustSystem.direction * gustSystem.strength * 30 * gustInfluence * stalkInfo.flexibility * stalkInfo.naturalSway;
                        totalRotation += gustRotation;
                    }
                }

                // Add subtle natural sway even without wind
                const naturalSway = Math.sin(Date.now() * 0.001 + index * 0.1) * 2 * stalkInfo.naturalSway;
                totalRotation += naturalSway;

                // Apply the combined rotation
                stalkInfo.element.style.transform = `rotate(${totalRotation}deg)`;
                stalkInfo.element.style.transformOrigin = 'bottom center';
                stalkInfo.element.style.transition = 'transform 0.3s ease-out';
            });
        }

        // Animation loop for continuous updates
        function animationLoop() {
            updateWheatAnimation();
            requestAnimationFrame(animationLoop);
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            generateWheatStalks();
            mouseX = window.innerWidth / 2; // Reset mouse position
            // Reset gust system
            gustSystem.active = false;
            gustSystem.nextGustTime = Date.now() + 2000;
        });

        // Initialize wheat field
        document.addEventListener('DOMContentLoaded', function() {
            generateWheatStalks();
            // Set initial gust timing
            gustSystem.nextGustTime = Date.now() + Math.random() * 3000 + 2000;
            // Start animation loop
            animationLoop();
        });
    </script>
</body>
</html>
