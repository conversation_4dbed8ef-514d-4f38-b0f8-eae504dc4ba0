<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Wheat stalks container -->
    <div class="wheat-field">
        <div class="wheat-stalk" data-index="0"></div>
        <div class="wheat-stalk" data-index="1"></div>
        <div class="wheat-stalk" data-index="2"></div>
        <div class="wheat-stalk" data-index="3"></div>
        <div class="wheat-stalk" data-index="4"></div>
        <div class="wheat-stalk" data-index="5"></div>
        <div class="wheat-stalk" data-index="6"></div>
        <div class="wheat-stalk" data-index="7"></div>
        <div class="wheat-stalk" data-index="8"></div>
        <div class="wheat-stalk" data-index="9"></div>
        <div class="wheat-stalk" data-index="10"></div>
        <div class="wheat-stalk" data-index="11"></div>
        <div class="wheat-stalk" data-index="12"></div>
        <div class="wheat-stalk" data-index="13"></div>
        <div class="wheat-stalk" data-index="14"></div>
        <div class="wheat-stalk" data-index="15"></div>
        <div class="wheat-stalk" data-index="16"></div>
        <div class="wheat-stalk" data-index="17"></div>
        <div class="wheat-stalk" data-index="18"></div>
        <div class="wheat-stalk" data-index="19"></div>
        <div class="wheat-stalk" data-index="20"></div>
        <div class="wheat-stalk" data-index="21"></div>
        <div class="wheat-stalk" data-index="22"></div>
        <div class="wheat-stalk" data-index="23"></div>
        <div class="wheat-stalk" data-index="24"></div>
        <div class="wheat-stalk" data-index="25"></div>
        <div class="wheat-stalk" data-index="26"></div>
        <div class="wheat-stalk" data-index="27"></div>
        <div class="wheat-stalk" data-index="28"></div>
        <div class="wheat-stalk" data-index="29"></div>
        <div class="wheat-stalk" data-index="30"></div>
        <div class="wheat-stalk" data-index="31"></div>
        <div class="wheat-stalk" data-index="32"></div>
        <div class="wheat-stalk" data-index="33"></div>
        <div class="wheat-stalk" data-index="34"></div>
        <div class="wheat-stalk" data-index="35"></div>
        <div class="wheat-stalk" data-index="36"></div>
        <div class="wheat-stalk" data-index="37"></div>
        <div class="wheat-stalk" data-index="38"></div>
        <div class="wheat-stalk" data-index="39"></div>
        <div class="wheat-stalk" data-index="40"></div>
        <div class="wheat-stalk" data-index="41"></div>
        <div class="wheat-stalk" data-index="42"></div>
        <div class="wheat-stalk" data-index="43"></div>
        <div class="wheat-stalk" data-index="44"></div>
        <div class="wheat-stalk" data-index="45"></div>
        <div class="wheat-stalk" data-index="46"></div>
        <div class="wheat-stalk" data-index="47"></div>
        <div class="wheat-stalk" data-index="48"></div>
        <div class="wheat-stalk" data-index="49"></div>
        <div class="wheat-stalk" data-index="50"></div>
        <div class="wheat-stalk" data-index="51"></div>
        <div class="wheat-stalk" data-index="52"></div>
        <div class="wheat-stalk" data-index="53"></div>
        <div class="wheat-stalk" data-index="54"></div>
        <div class="wheat-stalk" data-index="55"></div>
        <div class="wheat-stalk" data-index="56"></div>
        <div class="wheat-stalk" data-index="57"></div>
        <div class="wheat-stalk" data-index="58"></div>
        <div class="wheat-stalk" data-index="59"></div>
        <div class="wheat-stalk" data-index="60"></div>
        <div class="wheat-stalk" data-index="61"></div>
        <div class="wheat-stalk" data-index="62"></div>
        <div class="wheat-stalk" data-index="63"></div>
        <div class="wheat-stalk" data-index="64"></div>
        <div class="wheat-stalk" data-index="65"></div>
        <div class="wheat-stalk" data-index="66"></div>
        <div class="wheat-stalk" data-index="67"></div>
        <div class="wheat-stalk" data-index="68"></div>
        <div class="wheat-stalk" data-index="69"></div>
        <div class="wheat-stalk" data-index="70"></div>
        <div class="wheat-stalk" data-index="71"></div>
        <div class="wheat-stalk" data-index="72"></div>
        <div class="wheat-stalk" data-index="73"></div>
        <div class="wheat-stalk" data-index="74"></div>
        <div class="wheat-stalk" data-index="75"></div>
        <div class="wheat-stalk" data-index="76"></div>
        <div class="wheat-stalk" data-index="77"></div>
        <div class="wheat-stalk" data-index="78"></div>
        <div class="wheat-stalk" data-index="79"></div>
        <div class="wheat-stalk" data-index="80"></div>
        <div class="wheat-stalk" data-index="81"></div>
        <div class="wheat-stalk" data-index="82"></div>
        <div class="wheat-stalk" data-index="83"></div>
        <div class="wheat-stalk" data-index="84"></div>
        <div class="wheat-stalk" data-index="85"></div>
        <div class="wheat-stalk" data-index="86"></div>
        <div class="wheat-stalk" data-index="87"></div>
        <div class="wheat-stalk" data-index="88"></div>
        <div class="wheat-stalk" data-index="89"></div>
        <div class="wheat-stalk" data-index="90"></div>
        <div class="wheat-stalk" data-index="91"></div>
        <div class="wheat-stalk" data-index="92"></div>
        <div class="wheat-stalk" data-index="93"></div>
        <div class="wheat-stalk" data-index="94"></div>
        <div class="wheat-stalk" data-index="95"></div>
        <div class="wheat-stalk" data-index="96"></div>
        <div class="wheat-stalk" data-index="97"></div>
        <div class="wheat-stalk" data-index="98"></div>
        <div class="wheat-stalk" data-index="99"></div>
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Wheat stalks wind animation
        let mouseX = window.innerWidth / 2; // Start at center
        const wheatStalks = document.querySelectorAll('.wheat-stalk');

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
            updateWheatAnimation();
        });

        function updateWheatAnimation() {
            const windowWidth = window.innerWidth;
            const centerZone = windowWidth * 0.1; // 10% center zone
            const centerX = windowWidth / 2;

            // Calculate distance from center
            const distanceFromCenter = Math.abs(mouseX - centerX);

            // Check if mouse is in center dead zone
            if (distanceFromCenter <= centerZone / 2) {
                // No wind in center zone
                wheatStalks.forEach(stalk => {
                    stalk.style.transform = 'rotate(0deg)';
                });
                return;
            }

            // Calculate wind direction and strength
            const direction = mouseX > centerX ? 1 : -1; // 1 for right, -1 for left
            const maxDistance = (windowWidth / 2) - (centerZone / 2);
            const windStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);

            // Apply wind effect to each stalk
            wheatStalks.forEach((stalk, index) => {
                const stalkVariation = 0.8 + (Math.sin(index * 0.5) * 0.4); // Variation between stalks
                const maxRotation = 25; // Maximum rotation in degrees
                const rotation = direction * windStrength * maxRotation * stalkVariation;

                stalk.style.transform = `rotate(${rotation}deg)`;
                stalk.style.transformOrigin = 'bottom center';
            });
        }

        // Initialize wheat animation
        document.addEventListener('DOMContentLoaded', function() {
            updateWheatAnimation();
        });
    </script>
</body>
</html>
