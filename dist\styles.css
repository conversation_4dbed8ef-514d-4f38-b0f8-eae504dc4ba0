/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
    min-height: 100vh;
    overflow: hidden;
}

/* Container for centering content */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.content {
    text-align: center;
    max-width: 600px;
    width: 100%;
}

/* Logo styles */
.logo-container {
    margin-bottom: 40px;
}

.logo {
    max-width: 300px;
    max-height: 200px;
    width: auto;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Text logo fallback */
.text-logo {
    display: none; /* Hidden by default */
    animation: fadeInUp 0.5s ease-out;
}

.text-logo h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: #8b4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    margin-top: 0;
}

/* Coming soon text */
.coming-soon h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 400;
    color: #5d4037;
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: fadeInUp 1s ease-out;
}

.coming-soon p {
    font-size: 1.2rem;
    color: #6d4c41;
    font-weight: 300;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.logo-container p {
    font-size: 1.2rem;
    color: #6d4c41;
    font-weight: 300;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 20px;
    }
    
    .logo {
        max-width: 250px;
        max-height: 150px;
    }
    
    .text-logo h1 {
        font-size: 2.5rem;
    }
    
    .coming-soon h2 {
        font-size: 2rem;
        letter-spacing: 1px;
    }
    
    .coming-soon p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .logo {
        max-width: 200px;
        max-height: 120px;
    }
    
    .text-logo h1 {
        font-size: 2rem;
    }
    
    .coming-soon h2 {
        font-size: 1.5rem;
    }
    
    .coming-soon p {
        font-size: 0.9rem;
    }
}

/* Add a subtle background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Wheat stalks styles */
.wheat-field {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 5%;
    pointer-events: none;
    z-index: 10;
}

.wheat-stalk {
    width: 2px;
    height: 80px;
    background: linear-gradient(to top,
        #4a4a2a 0%,     /* Dark brown/green base */
        #6b5b3d 20%,    /* Medium brown */
        #8b7355 40%,    /* Light brown */
        #a68b5b 60%,    /* Tan */
        #c4a373 80%,    /* Light tan */
        #d4b896 100%    /* Very light tan top */
    );
    border-radius: 1px;
    position: relative;
    transition: transform 0.3s ease-out;
    transform-origin: bottom center;
    opacity: 0.9;
}

/* Create realistic grass blade effect */
.wheat-stalk::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 30%,
        rgba(255, 255, 255, 0.2) 60%,
        rgba(255, 255, 255, 0.3) 100%
    );
    border-radius: 1px;
}

/* Add multiple berries/seeds along the stalk */
.wheat-stalk::after {
    content: '';
    position: absolute;
    top: 15%;
    left: -0.5px;
    width: 1px;
    height: 1px;
    background: #8b6914;
    border-radius: 50%;
    box-shadow:
        /* Left side berries */
        -1px 8px 0 0.5px #8b6914,
        -0.5px 16px 0 0.3px #a0751a,
        -1px 24px 0 0.4px #8b6914,
        -0.5px 32px 0 0.2px #a0751a,
        -1px 40px 0 0.5px #8b6914,
        -0.5px 48px 0 0.3px #a0751a,

        /* Right side berries */
        1px 6px 0 0.4px #a0751a,
        0.5px 14px 0 0.3px #8b6914,
        1px 22px 0 0.5px #a0751a,
        0.5px 30px 0 0.2px #8b6914,
        1px 38px 0 0.4px #a0751a,
        0.5px 46px 0 0.3px #8b6914,

        /* Center berries */
        0px 10px 0 0.2px #8b6914,
        0px 20px 0 0.3px #a0751a,
        0px 28px 0 0.2px #8b6914,
        0px 36px 0 0.4px #a0751a,
        0px 44px 0 0.2px #8b6914;
}

/* Vary the height and appearance of different stalks */
.wheat-stalk[data-index="1"] { height: 75px; }
.wheat-stalk[data-index="2"] { height: 85px; }
.wheat-stalk[data-index="3"] { height: 70px; }
.wheat-stalk[data-index="4"] { height: 90px; }
.wheat-stalk[data-index="5"] { height: 78px; }
.wheat-stalk[data-index="6"] { height: 82px; }
.wheat-stalk[data-index="7"] { height: 88px; }
.wheat-stalk[data-index="8"] { height: 73px; }
.wheat-stalk[data-index="9"] { height: 86px; }
.wheat-stalk[data-index="10"] { height: 79px; }
.wheat-stalk[data-index="11"] { height: 84px; }
.wheat-stalk[data-index="12"] { height: 77px; }
.wheat-stalk[data-index="13"] { height: 91px; }
.wheat-stalk[data-index="14"] { height: 74px; }
.wheat-stalk[data-index="15"] { height: 87px; }
.wheat-stalk[data-index="16"] { height: 81px; }
.wheat-stalk[data-index="17"] { height: 76px; }
.wheat-stalk[data-index="18"] { height: 89px; }
.wheat-stalk[data-index="19"] { height: 83px; }

/* Responsive wheat stalks */
@media (max-width: 768px) {
    .wheat-field {
        height: 80px;
        padding: 0 3%;
    }

    .wheat-stalk {
        width: 1.5px;
        height: 60px;
    }

    .wheat-stalk::after {
        width: 0.8px;
        height: 0.8px;
        top: 18%;
        box-shadow:
            /* Scaled down berries for mobile */
            -0.8px 6px 0 0.3px #8b6914,
            -0.4px 12px 0 0.2px #a0751a,
            -0.8px 18px 0 0.3px #8b6914,
            -0.4px 24px 0 0.2px #a0751a,
            -0.8px 30px 0 0.3px #8b6914,

            0.8px 4px 0 0.2px #a0751a,
            0.4px 10px 0 0.2px #8b6914,
            0.8px 16px 0 0.3px #a0751a,
            0.4px 22px 0 0.2px #8b6914,
            0.8px 28px 0 0.2px #a0751a,

            0px 8px 0 0.2px #8b6914,
            0px 14px 0 0.2px #a0751a,
            0px 20px 0 0.2px #8b6914,
            0px 26px 0 0.2px #a0751a;
    }

    /* Adjust heights for mobile */
    .wheat-stalk[data-index="1"] { height: 55px; }
    .wheat-stalk[data-index="2"] { height: 65px; }
    .wheat-stalk[data-index="3"] { height: 50px; }
    .wheat-stalk[data-index="4"] { height: 70px; }
    .wheat-stalk[data-index="5"] { height: 58px; }
    .wheat-stalk[data-index="6"] { height: 62px; }
    .wheat-stalk[data-index="7"] { height: 68px; }
    .wheat-stalk[data-index="8"] { height: 53px; }
    .wheat-stalk[data-index="9"] { height: 66px; }
    .wheat-stalk[data-index="10"] { height: 59px; }
    .wheat-stalk[data-index="11"] { height: 64px; }
    .wheat-stalk[data-index="12"] { height: 57px; }
    .wheat-stalk[data-index="13"] { height: 71px; }
    .wheat-stalk[data-index="14"] { height: 54px; }
    .wheat-stalk[data-index="15"] { height: 67px; }
    .wheat-stalk[data-index="16"] { height: 61px; }
    .wheat-stalk[data-index="17"] { height: 56px; }
    .wheat-stalk[data-index="18"] { height: 69px; }
    .wheat-stalk[data-index="19"] { height: 63px; }
}

@media (max-width: 480px) {
    .wheat-field {
        height: 60px;
        padding: 0 2%;
    }

    .wheat-stalk {
        width: 1px;
        height: 45px;
    }

    .wheat-stalk::after {
        width: 0.5px;
        height: 0.5px;
        top: 20%;
        box-shadow:
            /* Very small berries for small mobile */
            -0.5px 4px 0 0.2px #8b6914,
            -0.3px 8px 0 0.1px #a0751a,
            -0.5px 12px 0 0.2px #8b6914,
            -0.3px 16px 0 0.1px #a0751a,
            -0.5px 20px 0 0.2px #8b6914,

            0.5px 3px 0 0.1px #a0751a,
            0.3px 7px 0 0.1px #8b6914,
            0.5px 11px 0 0.2px #a0751a,
            0.3px 15px 0 0.1px #8b6914,
            0.5px 19px 0 0.1px #a0751a,

            0px 5px 0 0.1px #8b6914,
            0px 9px 0 0.1px #a0751a,
            0px 13px 0 0.1px #8b6914,
            0px 17px 0 0.1px #a0751a;
    }

    /* Further adjust heights for small mobile */
    .wheat-stalk[data-index="1"] { height: 42px; }
    .wheat-stalk[data-index="2"] { height: 48px; }
    .wheat-stalk[data-index="3"] { height: 40px; }
    .wheat-stalk[data-index="4"] { height: 50px; }
    .wheat-stalk[data-index="5"] { height: 44px; }
    .wheat-stalk[data-index="6"] { height: 46px; }
    .wheat-stalk[data-index="7"] { height: 49px; }
    .wheat-stalk[data-index="8"] { height: 41px; }
    .wheat-stalk[data-index="9"] { height: 47px; }
    .wheat-stalk[data-index="10"] { height: 43px; }
    .wheat-stalk[data-index="11"] { height: 46px; }
    .wheat-stalk[data-index="12"] { height: 42px; }
    .wheat-stalk[data-index="13"] { height: 51px; }
    .wheat-stalk[data-index="14"] { height: 41px; }
    .wheat-stalk[data-index="15"] { height: 48px; }
    .wheat-stalk[data-index="16"] { height: 45px; }
    .wheat-stalk[data-index="17"] { height: 43px; }
    .wheat-stalk[data-index="18"] { height: 49px; }
    .wheat-stalk[data-index="19"] { height: 46px; }
}
